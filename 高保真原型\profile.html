<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的 - 小柿子识字工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="common.css">
</head>
<body>
    <div class="phone-container">
        <div class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 小程序导航栏 -->
            <div class="nav-header">
                <span>👤 我的</span>
            </div>
            
            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 用户信息卡片 -->
                <div class="p-4">
                    <div class="card">
                        <div class="flex items-center gap-4">
                            <div class="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center text-white text-2xl">
                                👶
                            </div>
                            <div class="flex-1">
                                <h3 class="text-lg font-bold text-gray-800">小明</h3>
                                <p class="text-gray-600 text-sm">中班 · 5岁</p>
                                <p class="text-gray-500 text-xs">最后学习：今天 09:30</p>
                            </div>
                            <button class="text-gray-400">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 学习统计 -->
                <div class="px-4">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">📊 学习统计</h3>
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <div class="text-2xl font-bold text-green-600">156</div>
                                <div class="text-xs text-gray-500">累计学习</div>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-orange-500">12</div>
                                <div class="text-xs text-gray-500">连续天数</div>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-blue-500">89%</div>
                                <div class="text-xs text-gray-500">掌握率</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 功能菜单 -->
                <div class="px-4">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">🛠️ 功能管理</h3>
                        
                        <div class="space-y-1">
                            <!-- 幼儿信息管理 -->
                            <div class="list-item">
                                <div class="flex items-center gap-3 flex-1">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                        👶
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">幼儿信息管理</p>
                                        <p class="text-xs text-gray-500">设置生日、班级等信息</p>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            
                            <!-- 字词库管理 -->
                            <div class="list-item">
                                <div class="flex items-center gap-3 flex-1">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                        📚
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">字词库管理</p>
                                        <p class="text-xs text-gray-500">添加、编辑、删除字词</p>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            
                            <!-- 学习记录 -->
                            <div class="list-item">
                                <div class="flex items-center gap-3 flex-1">
                                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                        📊
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">学习记录</p>
                                        <p class="text-xs text-gray-500">查看详细学习数据</p>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            
                            <!-- 家长指导手册 -->
                            <div class="list-item">
                                <div class="flex items-center gap-3 flex-1">
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                        📖
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">家长指导手册</p>
                                        <p class="text-xs text-gray-500">科学识字方法与技巧</p>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 设置选项 -->
                <div class="px-4">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">⚙️ 设置</h3>
                        
                        <div class="space-y-1">
                            <!-- 复习提醒 -->
                            <div class="list-item">
                                <div class="flex items-center gap-3 flex-1">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                        🔔
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">复习提醒</p>
                                        <p class="text-xs text-gray-500">设置学习提醒时间</p>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="text-sm text-gray-500">已开启</span>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </div>
                            
                            <!-- 数据导出 -->
                            <div class="list-item">
                                <div class="flex items-center gap-3 flex-1">
                                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                                        📤
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">数据导出</p>
                                        <p class="text-xs text-gray-500">备份学习数据</p>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            
                            <!-- 关于我们 -->
                            <div class="list-item">
                                <div class="flex items-center gap-3 flex-1">
                                    <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                                        ℹ️
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">关于我们</p>
                                        <p class="text-xs text-gray-500">版本信息与反馈</p>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 数据风险提示 -->
                <div class="px-4 pb-6">
                    <div class="p-4 bg-orange-50 rounded-lg border-l-4 border-orange-400">
                        <div class="flex items-start gap-2">
                            <i class="fas fa-exclamation-triangle text-orange-500 mt-1"></i>
                            <div>
                                <h4 class="font-medium text-orange-800 mb-1">数据存储提示</h4>
                                <p class="text-orange-700 text-sm">
                                    本工具所有学习数据仅保存在您的设备本地，一旦删除本程序或设备故障导致数据丢失，无法通过任何方式找回。
                                    建议定期使用"数据导出"功能备份重要数据。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 底部Tab导航 -->
            <div class="tab-bar">
                <div class="tab-item">
                    <div class="tab-icon">🏠</div>
                    <span>首页</span>
                </div>
                <div class="tab-item">
                    <div class="tab-icon">📚</div>
                    <span>复习</span>
                </div>
                <div class="tab-item active">
                    <div class="tab-icon">👤</div>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 添加列表项点击效果
        document.querySelectorAll('.list-item').forEach(item => {
            item.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                this.style.background = 'var(--light-green)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                    this.style.background = 'white';
                }, 150);
            });
        });
        
        // Tab切换效果
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab-item').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
