生成符合微信小程序规范的高保真的原型图，请通过以下方式帮我完成所有界面的原型设计，并确保这些原型界面可以直接用于开发。

首先：仔细阅读[2-详细功能需求文档​]和[3-原型设计规范]、[4-微信小程序数据库设计文档规范]，并确保你理解了所有的功能需求和设计规范。
然后进行高保真 UI 设计：
1. 作为 UI 设计师，设计贴近真实小程序设计规范的界面，使用现代化的 UI 元素，使其具有良好的视觉体验。
2. HTML 原型实现：使用 HTML + Tailwind CSS（或 Bootstrap）生成所有原型界面，并使用 FontAwesome（或其他开源 UI 组件）让界面更加精美、接近真实的 App 设计。拆分代码文件，保持结构清晰。
   - 文件结构规划：每个界面应作为独立的 HTML 文件存放，例如 home.html、profile.html、settings.html 等。index.html 作为主入口，不直接写入所有界面的 HTML 代码，而是使用 iframe 的方式嵌入这些 HTML 片段，并将所有页面直接平铺展示在 index 页面中，而不是跳转链接。
   - 生成HTML文件统一放在`高保真原型`文件夹下。
   - 模拟真实界面：
      - 界面尺寸应模拟 iPhone 15 Pro，并让界面圆角化，使其更像真实的手机界面。
      - 使用真实的 UI 图片，而非占位符图片（可从 Unsplash、Pexels、Apple 官方 UI 资源中选择，合适的话，优先使用Emoji图标）。
      - 添加顶部状态栏（模拟 iOS 状态栏），并包含小程序导航栏（类似 iOS 底部 Tab Bar）

#### **补充说明**  
- 数据存储风险提示需在设置页及首次操作时明显但不干扰主流程。  
- 界面需体现“高效工具”定位，减少层级跳转，核心功能一步直达。  



## 修改意见：
1. 我的页面：
   - 去掉“数据导出”功能
2. 字词复习详情页面：
   - 去掉“发音”、“提示”和“跳过”
2. 添加字词页面：
   - 去掉手动输入方式；
   - 批量导入：采用逗号、分号、顿号等常见分隔符自动分割字词。
   - 去掉“快速模板”
2. 设置页面：
   - 去掉“导出学习数据”、“导入学习数据”
   - 去掉“用户协议”、“隐私政策”
   - “意见反馈”直接用户关注对应的公众号
   - 每日复习数量改为“每次复习数量”，一天可以复习多次。

