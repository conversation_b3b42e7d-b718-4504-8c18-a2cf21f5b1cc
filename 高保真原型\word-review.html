<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字词复习 - 小柿子识字工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="common.css">
</head>
<body>
    <div class="phone-container">
        <div class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 小程序导航栏 -->
            <div class="nav-header">
                <i class="nav-back fas fa-chevron-left"></i>
                <span>字词复习</span>
            </div>
            
            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 进度指示器 -->
                <div class="p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-600">复习进度</span>
                        <span class="text-sm font-medium text-green-600">3/8</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 37.5%"></div>
                    </div>
                </div>
                
                <!-- 字词显示区域 -->
                <div class="flex-1 flex flex-col items-center justify-center px-6">
                    <!-- 大字显示 -->
                    <div class="text-center mb-8">
                        <div class="big-character">花</div>
                        <div class="text-xl text-gray-600 mb-2">花朵</div>
                        <div class="text-sm text-gray-500">今日新学字词</div>
                    </div>
                    
                    <!-- 提示信息 -->
                    <div class="card w-full mb-8">
                        <div class="text-center">
                            <div class="text-4xl mb-3">🌸</div>
                            <h3 class="text-lg font-bold text-gray-800 mb-2">和孩子一起认识这个字</h3>
                            <p class="text-gray-600 text-sm leading-relaxed">
                                可以问问孩子："这是什么字？"<br>
                                "这个字可以组成什么词？"<br>
                                "在什么场景里会用到这个字？"
                            </p>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="w-full space-y-4">
                        <!-- 主要操作按钮 -->
                        <div class="grid grid-cols-2 gap-4">
                            <button class="btn-primary flex items-center justify-center gap-2 py-4 text-lg">
                                <i class="fas fa-check"></i>
                                已掌握
                            </button>
                            <button class="btn-secondary flex items-center justify-center gap-2 py-4 text-lg">
                                <i class="fas fa-redo"></i>
                                需复习
                            </button>
                        </div>
                        

                    </div>
                </div>
                
                <!-- 底部导航 -->
                <div class="p-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <button class="flex items-center gap-2 text-gray-500">
                            <i class="fas fa-chevron-left"></i>
                            <span class="text-sm">上一个</span>
                        </button>
                        
                        <div class="flex gap-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                            <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                            <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                            <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                            <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                        </div>
                        
                        <button class="flex items-center gap-2 text-green-600">
                            <span class="text-sm">下一个</span>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 完成弹窗 (隐藏状态) -->
    <div id="completeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" style="display: none;">
        <div class="bg-white rounded-2xl p-6 m-6 max-w-sm w-full">
            <div class="text-center">
                <div class="text-6xl mb-4">🎉</div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">复习完成！</h3>
                <p class="text-gray-600 mb-4">今天复习了 8 个字词</p>
                
                <div class="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">6</div>
                        <div class="text-gray-500">已掌握</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">2</div>
                        <div class="text-gray-500">需复习</div>
                    </div>
                </div>
                
                <button class="btn-primary w-full">返回首页</button>
            </div>
        </div>
    </div>
    
    <script>
        let currentWord = 3;
        const totalWords = 8;
        
        // 更新进度
        function updateProgress() {
            const progress = (currentWord / totalWords) * 100;
            document.querySelector('.progress-fill').style.width = progress + '%';
            document.querySelector('.text-green-600').textContent = `${currentWord}/${totalWords}`;
            
            // 更新底部指示器
            const indicators = document.querySelectorAll('.w-2.h-2');
            indicators.forEach((indicator, index) => {
                if (index < currentWord) {
                    indicator.className = 'w-2 h-2 bg-green-500 rounded-full';
                } else {
                    indicator.className = 'w-2 h-2 bg-gray-300 rounded-full';
                }
            });
        }
        
        // 字词数据
        const words = [
            { char: '太', word: '太阳', emoji: '☀️' },
            { char: '阳', word: '阳光', emoji: '🌞' },
            { char: '花', word: '花朵', emoji: '🌸' },
            { char: '草', word: '小草', emoji: '🌱' },
            { char: '树', word: '大树', emoji: '🌳' },
            { char: '叶', word: '叶子', emoji: '🍃' },
            { char: '水', word: '水滴', emoji: '💧' },
            { char: '火', word: '火焰', emoji: '🔥' }
        ];
        
        // 显示当前字词
        function showCurrentWord() {
            const word = words[currentWord - 1];
            document.querySelector('.big-character').textContent = word.char;
            document.querySelector('.text-xl').textContent = word.word;
            document.querySelector('.text-4xl').textContent = word.emoji;
        }
        
        // 按钮点击事件
        document.querySelector('.btn-primary').addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
                nextWord();
            }, 150);
        });
        
        document.querySelector('.btn-secondary').addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
                nextWord();
            }, 150);
        });
        
        // 下一个字词
        function nextWord() {
            if (currentWord < totalWords) {
                currentWord++;
                updateProgress();
                showCurrentWord();
            } else {
                // 显示完成弹窗
                document.getElementById('completeModal').style.display = 'flex';
            }
        }
        
        // 上一个/下一个按钮
        document.querySelector('.text-gray-500').parentElement.addEventListener('click', function() {
            if (currentWord > 1) {
                currentWord--;
                updateProgress();
                showCurrentWord();
            }
        });
        
        document.querySelector('.text-green-600').parentElement.addEventListener('click', function() {
            nextWord();
        });
        

        
        // 返回按钮
        document.querySelector('.nav-back').addEventListener('click', function() {
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
        
        // 初始化
        updateProgress();
        showCurrentWord();
    </script>
</body>
</html>
