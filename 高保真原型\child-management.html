<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>幼儿信息管理 - 小柿子识字工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="common.css">
</head>
<body>
    <div class="phone-container">
        <div class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 小程序导航栏 -->
            <div class="nav-header">
                <i class="nav-back fas fa-chevron-left"></i>
                <span>幼儿信息管理</span>
            </div>
            
            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 当前幼儿信息 -->
                <div class="p-4">
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-gray-800">👶 当前幼儿</h3>
                            <button class="text-green-600 text-sm font-medium">编辑</button>
                        </div>
                        
                        <div class="flex items-center gap-4 mb-4">
                            <div class="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center text-white text-2xl">
                                👶
                            </div>
                            <div class="flex-1">
                                <h4 class="text-xl font-bold text-gray-800">小明</h4>
                                <p class="text-gray-600">中班 · 5岁</p>
                                <p class="text-gray-500 text-sm">生日：2019年3月</p>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 text-center">
                            <div class="p-3 bg-green-50 rounded-lg">
                                <div class="text-lg font-bold text-green-600">156</div>
                                <div class="text-xs text-gray-600">累计学习</div>
                            </div>
                            <div class="p-3 bg-blue-50 rounded-lg">
                                <div class="text-lg font-bold text-blue-600">12</div>
                                <div class="text-xs text-gray-600">连续天数</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 基本信息设置 -->
                <div class="px-4">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">📝 基本信息</h3>
                        
                        <div class="space-y-4">
                            <!-- 姓名 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">幼儿姓名</label>
                                <input type="text" class="input-field" value="小明" placeholder="请输入幼儿姓名">
                            </div>
                            
                            <!-- 生日设置 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">生日</label>
                                <div class="grid grid-cols-2 gap-3">
                                    <select class="input-field">
                                        <option>2024年</option>
                                        <option>2023年</option>
                                        <option>2022年</option>
                                        <option>2021年</option>
                                        <option>2020年</option>
                                        <option selected>2019年</option>
                                        <option>2018年</option>
                                        <option>2017年</option>
                                        <option>2016年</option>
                                        <option>2015年</option>
                                        <option>2014年</option>
                                    </select>
                                    <select class="input-field">
                                        <option>1月</option>
                                        <option>2月</option>
                                        <option selected>3月</option>
                                        <option>4月</option>
                                        <option>5月</option>
                                        <option>6月</option>
                                        <option>7月</option>
                                        <option>8月</option>
                                        <option>9月</option>
                                        <option>10月</option>
                                        <option>11月</option>
                                        <option>12月</option>
                                    </select>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">系统将自动计算年龄</p>
                            </div>
                            
                            <!-- 班级设置 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">所在班级</label>
                                <div class="grid grid-cols-3 gap-2">
                                    <button class="btn-outline text-sm py-3" data-class="小班">
                                        🐣 小班
                                    </button>
                                    <button class="btn-primary text-sm py-3" data-class="中班">
                                        🐤 中班
                                    </button>
                                    <button class="btn-outline text-sm py-3" data-class="大班">
                                        🐥 大班
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 性别选择 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">性别</label>
                                <div class="grid grid-cols-2 gap-3">
                                    <button class="btn-primary text-sm py-3" data-gender="男">
                                        👦 男孩
                                    </button>
                                    <button class="btn-outline text-sm py-3" data-gender="女">
                                        👧 女孩
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 学习偏好设置 -->
                <div class="px-4">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">🎯 学习偏好</h3>
                        
                        <div class="space-y-4">
                            <!-- 学习兴趣 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">兴趣标签</label>
                                <div class="flex flex-wrap gap-2">
                                    <button class="tag active-tag">🌸 花草</button>
                                    <button class="tag">🐱 动物</button>
                                    <button class="tag active-tag">🚗 交通</button>
                                    <button class="tag">🍎 水果</button>
                                    <button class="tag">🏠 建筑</button>
                                    <button class="tag">🎨 颜色</button>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">选择孩子感兴趣的内容，系统会优先推荐相关字词</p>
                            </div>
                            
                            <!-- 学习难度 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">学习难度</label>
                                <div class="grid grid-cols-3 gap-2">
                                    <button class="btn-outline text-sm py-2">
                                        😊 简单
                                    </button>
                                    <button class="btn-primary text-sm py-2">
                                        🤔 适中
                                    </button>
                                    <button class="btn-outline text-sm py-2">
                                        😤 困难
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 多幼儿管理 -->
                <div class="px-4">
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-gray-800">👨‍👩‍👧‍👦 多幼儿管理</h3>
                            <button class="btn-outline text-sm px-3 py-1">
                                <i class="fas fa-plus mr-1"></i>添加
                            </button>
                        </div>
                        
                        <div class="space-y-2">
                            <!-- 当前幼儿 -->
                            <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg border-2 border-green-200">
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">
                                        👶
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">小明</p>
                                        <p class="text-xs text-gray-500">中班 · 5岁</p>
                                    </div>
                                </div>
                                <span class="text-xs text-green-600 font-medium">当前</span>
                            </div>
                            
                            <!-- 其他幼儿 -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm">
                                        👧
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">小红</p>
                                        <p class="text-xs text-gray-500">大班 · 6岁</p>
                                    </div>
                                </div>
                                <button class="text-gray-400 text-sm">切换</button>
                            </div>
                        </div>
                        
                        <p class="text-xs text-gray-500 mt-3">
                            💡 支持管理多个幼儿的学习数据，每个幼儿的数据独立保存
                        </p>
                    </div>
                </div>
                
                <!-- 保存按钮 -->
                <div class="px-4 pb-6">
                    <button class="btn-primary w-full flex items-center justify-center gap-2 py-4">
                        <i class="fas fa-save"></i>
                        保存设置
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 班级选择
        document.querySelectorAll('[data-class]').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('[data-class]').forEach(b => {
                    b.classList.remove('btn-primary');
                    b.classList.add('btn-outline');
                });
                this.classList.remove('btn-outline');
                this.classList.add('btn-primary');
                
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
        
        // 性别选择
        document.querySelectorAll('[data-gender]').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('[data-gender]').forEach(b => {
                    b.classList.remove('btn-primary');
                    b.classList.add('btn-outline');
                });
                this.classList.remove('btn-outline');
                this.classList.add('btn-primary');
                
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
        
        // 兴趣标签切换
        document.querySelectorAll('.tag').forEach(tag => {
            tag.addEventListener('click', function() {
                this.classList.toggle('active-tag');
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
        
        // 学习难度选择
        document.querySelectorAll('.grid .btn-outline, .grid .btn-primary').forEach(btn => {
            if (btn.textContent.includes('简单') || btn.textContent.includes('适中') || btn.textContent.includes('困难')) {
                btn.addEventListener('click', function() {
                    this.parentElement.querySelectorAll('button').forEach(b => {
                        b.classList.remove('btn-primary');
                        b.classList.add('btn-outline');
                    });
                    this.classList.remove('btn-outline');
                    this.classList.add('btn-primary');
                    
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            }
        });
        
        // 保存按钮
        document.querySelector('.btn-primary.w-full').addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
                alert('设置已保存！');
            }, 150);
        });
        
        // 添加幼儿按钮
        document.querySelector('.btn-outline').addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
                alert('添加新幼儿功能演示');
            }, 150);
        });
        
        // 切换幼儿
        document.querySelector('.text-gray-400').addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
                alert('切换到小红的学习数据');
            }, 150);
        });
        
        // 返回按钮
        document.querySelector('.nav-back').addEventListener('click', function() {
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
        
        // 添加active-tag样式
        const style = document.createElement('style');
        style.textContent = `
            .active-tag {
                background: var(--primary-green) !important;
                color: white !important;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
