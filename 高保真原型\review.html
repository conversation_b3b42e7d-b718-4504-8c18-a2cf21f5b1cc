<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复习页 - 小柿子识字工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="common.css">
</head>
<body>
    <div class="phone-container">
        <div class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 小程序导航栏 -->
            <div class="nav-header">
                <span>📚 智能复习</span>
            </div>
            
            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 复习计划概览 -->
                <div class="p-4">
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-gray-800">🧠 艾宾浩斯复习计划</h3>
                            <span class="tag orange">智能推荐</span>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center p-3 bg-green-50 rounded-lg">
                                <div class="text-2xl font-bold text-green-600">12</div>
                                <div class="text-xs text-gray-600">今日待复习</div>
                            </div>
                            <div class="text-center p-3 bg-orange-50 rounded-lg">
                                <div class="text-2xl font-bold text-orange-600">5</div>
                                <div class="text-xs text-gray-600">薄弱字词</div>
                            </div>
                        </div>
                        
                        <button class="btn-primary w-full flex items-center justify-center gap-2">
                            <i class="fas fa-brain"></i>
                            开始智能复习
                        </button>
                    </div>
                </div>
                
                <!-- 复习日历 -->
                <div class="px-4">
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-gray-800">📅 复习日历</h3>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-chevron-left text-gray-400"></i>
                                <span class="text-sm font-medium">6月</span>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                        
                        <!-- 日历网格 -->
                        <div class="grid grid-cols-7 gap-1 mb-4">
                            <!-- 星期标题 -->
                            <div class="text-center text-xs text-gray-500 py-2">日</div>
                            <div class="text-center text-xs text-gray-500 py-2">一</div>
                            <div class="text-center text-xs text-gray-500 py-2">二</div>
                            <div class="text-center text-xs text-gray-500 py-2">三</div>
                            <div class="text-center text-xs text-gray-500 py-2">四</div>
                            <div class="text-center text-xs text-gray-500 py-2">五</div>
                            <div class="text-center text-xs text-gray-500 py-2">六</div>
                            
                            <!-- 日期 -->
                            <div class="text-center text-sm py-2 text-gray-300">30</div>
                            <div class="text-center text-sm py-2 text-gray-300">31</div>
                            <div class="text-center text-sm py-2 text-gray-800">1</div>
                            <div class="text-center text-sm py-2 text-gray-800">2</div>
                            <div class="text-center text-sm py-2 bg-green-100 rounded text-green-800 font-medium">3</div>
                            <div class="text-center text-sm py-2 text-gray-800">4</div>
                            <div class="text-center text-sm py-2 bg-orange-100 rounded text-orange-800 font-medium">5</div>
                            
                            <div class="text-center text-sm py-2 text-gray-800">6</div>
                            <div class="text-center text-sm py-2 bg-green-100 rounded text-green-800 font-medium">7</div>
                            <div class="text-center text-sm py-2 text-gray-800">8</div>
                            <div class="text-center text-sm py-2 bg-blue-100 rounded text-blue-800 font-medium">9</div>
                            <div class="text-center text-sm py-2 text-gray-800">10</div>
                            <div class="text-center text-sm py-2 bg-red-100 rounded text-red-800 font-medium">11</div>
                            <div class="text-center text-sm py-2 text-gray-800">12</div>
                        </div>
                        
                        <!-- 图例 -->
                        <div class="grid grid-cols-2 gap-2 text-xs">
                            <div class="flex items-center gap-2">
                                <div class="w-3 h-3 bg-green-100 rounded"></div>
                                <span class="text-gray-600">第1-2天复习</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="w-3 h-3 bg-orange-100 rounded"></div>
                                <span class="text-gray-600">第4天复习</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="w-3 h-3 bg-blue-100 rounded"></div>
                                <span class="text-gray-600">第7天复习</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="w-3 h-3 bg-red-100 rounded"></div>
                                <span class="text-gray-600">第15天复习</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 复习任务列表 -->
                <div class="px-4">
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-gray-800">📋 今日复习任务</h3>
                            <span class="text-sm text-gray-500">12个字词</span>
                        </div>
                        
                        <div class="space-y-3">
                            <!-- 复习阶段1 -->
                            <div class="border border-green-200 rounded-lg p-3">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-green-700">第1天复习 (新学)</span>
                                    <span class="tag">5个</span>
                                </div>
                                <div class="flex flex-wrap gap-2">
                                    <span class="px-2 py-1 bg-green-50 text-green-700 rounded text-sm">花</span>
                                    <span class="px-2 py-1 bg-green-50 text-green-700 rounded text-sm">草</span>
                                    <span class="px-2 py-1 bg-green-50 text-green-700 rounded text-sm">树</span>
                                    <span class="px-2 py-1 bg-green-50 text-green-700 rounded text-sm">叶</span>
                                    <span class="px-2 py-1 bg-green-50 text-green-700 rounded text-sm">根</span>
                                </div>
                            </div>
                            
                            <!-- 复习阶段2 -->
                            <div class="border border-orange-200 rounded-lg p-3">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-orange-700">第4天复习</span>
                                    <span class="tag orange">4个</span>
                                </div>
                                <div class="flex flex-wrap gap-2">
                                    <span class="px-2 py-1 bg-orange-50 text-orange-700 rounded text-sm">太</span>
                                    <span class="px-2 py-1 bg-orange-50 text-orange-700 rounded text-sm">阳</span>
                                    <span class="px-2 py-1 bg-orange-50 text-orange-700 rounded text-sm">月</span>
                                    <span class="px-2 py-1 bg-orange-50 text-orange-700 rounded text-sm">星</span>
                                </div>
                            </div>
                            
                            <!-- 复习阶段3 -->
                            <div class="border border-blue-200 rounded-lg p-3">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-blue-700">第7天复习</span>
                                    <span class="tag" style="background: #3B82F6; color: white;">3个</span>
                                </div>
                                <div class="flex flex-wrap gap-2">
                                    <span class="px-2 py-1 bg-blue-50 text-blue-700 rounded text-sm">水</span>
                                    <span class="px-2 py-1 bg-blue-50 text-blue-700 rounded text-sm">火</span>
                                    <span class="px-2 py-1 bg-blue-50 text-blue-700 rounded text-sm">土</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 薄弱字词专项 -->
                <div class="px-4 pb-6">
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-gray-800">⚠️ 薄弱字词专项</h3>
                            <span class="text-sm text-red-600">需要加强</span>
                        </div>
                        
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-2 bg-red-50 rounded">
                                <div class="flex items-center gap-2">
                                    <span class="text-lg font-bold">雨</span>
                                    <span class="text-sm text-gray-600">错误3次</span>
                                </div>
                                <button class="btn-outline text-xs px-3 py-1">加强练习</button>
                            </div>
                            <div class="flex items-center justify-between p-2 bg-red-50 rounded">
                                <div class="flex items-center gap-2">
                                    <span class="text-lg font-bold">雪</span>
                                    <span class="text-sm text-gray-600">错误2次</span>
                                </div>
                                <button class="btn-outline text-xs px-3 py-1">加强练习</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 底部Tab导航 -->
            <div class="tab-bar">
                <div class="tab-item">
                    <div class="tab-icon">🏠</div>
                    <span>首页</span>
                </div>
                <div class="tab-item active">
                    <div class="tab-icon">📚</div>
                    <span>复习</span>
                </div>
                <div class="tab-item">
                    <div class="tab-icon">👤</div>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 添加按钮点击效果
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
        
        // 日历日期点击效果
        document.querySelectorAll('.grid div').forEach(day => {
            if (day.textContent && !isNaN(day.textContent)) {
                day.addEventListener('click', function() {
                    this.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            }
        });
    </script>
</body>
</html>
