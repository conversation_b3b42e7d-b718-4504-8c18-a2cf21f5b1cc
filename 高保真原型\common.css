/* 小柿子·幼儿识字辅助工具 - 通用样式 */

/* 自定义护眼色调变量 */
:root {
    --primary-green: #7FB069;
    --light-green: #B8E6B8;
    --soft-green: #E8F5E8;
    --soft-orange: #F4A261;
    --warm-beige: #F7F3E9;
    --text-dark: #2D3748;
    --text-light: #718096;
    --border-light: #E2E8F0;
    --shadow-soft: 0 4px 12px rgba(0,0,0,0.1);
}

/* 手机容器样式 - 已移除，直接使用phone-screen */

.phone-screen {
    width: 100%;
    height: 100%;
    background: var(--warm-beige);
    border-radius: 0;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
}

/* iOS状态栏 */
.status-bar {
    height: 44px;
    background: var(--warm-beige);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-dark);
    border-bottom: 1px solid var(--border-light);
}

.status-left {
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 小程序导航栏 */
.nav-header {
    height: 60px;
    background: var(--primary-green);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    font-weight: bold;
    position: relative;
}

.nav-back {
    position: absolute;
    left: 20px;
    color: white;
    font-size: 20px;
    cursor: pointer;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    overflow-y: auto;
    background: var(--warm-beige);
}

/* 底部Tab导航 */
.tab-bar {
    height: 80px;
    background: white;
    border-top: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding-bottom: 20px;
}

.tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    color: var(--text-light);
    font-size: 12px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.tab-item.active {
    color: var(--primary-green);
}

.tab-icon {
    font-size: 24px;
}

/* 通用按钮样式 */
.btn-primary {
    background: var(--primary-green);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-soft);
}

.btn-primary:hover {
    background: #6FA055;
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--soft-orange);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-soft);
}

.btn-outline {
    background: transparent;
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
    border-radius: 12px;
    padding: 10px 22px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: var(--primary-green);
    color: white;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    margin: 16px;
    box-shadow: var(--shadow-soft);
    border: 1px solid var(--border-light);
}

/* 大字显示样式 */
.big-character {
    font-family: 'KaiTi', '楷体', serif;
    font-size: 120px;
    font-weight: bold;
    color: var(--text-dark);
    text-align: center;
    line-height: 1.2;
    margin: 40px 0;
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--soft-green);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-green);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* 输入框样式 */
.input-field {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-light);
    border-radius: 12px;
    font-size: 16px;
    background: white;
    transition: border-color 0.3s ease;
}

.input-field:focus {
    outline: none;
    border-color: var(--primary-green);
}

/* 列表项样式 */
.list-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: white;
    border-bottom: 1px solid var(--border-light);
    cursor: pointer;
    transition: background 0.3s ease;
}

.list-item:hover {
    background: var(--soft-green);
}

.list-item:last-child {
    border-bottom: none;
}

/* 标签样式 */
.tag {
    display: inline-block;
    padding: 4px 12px;
    background: var(--light-green);
    color: var(--text-dark);
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.tag.orange {
    background: var(--soft-orange);
    color: white;
}

/* 响应式设计 */
@media (max-width: 390px) {
    .phone-container {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
        padding: 0;
    }
    
    .phone-screen {
        border-radius: 0;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.font-bold { font-weight: bold; }
.font-medium { font-weight: 500; }
.mb-4 { margin-bottom: 16px; }
.mt-4 { margin-top: 16px; }
.p-4 { padding: 16px; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-4 { gap: 16px; }
.w-full { width: 100%; }
.h-full { height: 100%; }
