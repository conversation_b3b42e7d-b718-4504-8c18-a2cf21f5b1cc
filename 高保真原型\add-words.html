<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加字词 - 小柿子识字工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="common.css">
</head>
<body>
    <div class="phone-container">
        <div class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 小程序导航栏 -->
            <div class="nav-header">
                <i class="nav-back fas fa-chevron-left"></i>
                <span>添加字词</span>
            </div>
            
            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 添加方式选择 -->
                <div class="p-4">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">📝 选择添加方式</h3>
                        
                        <div class="grid grid-cols-2 gap-3">
                            <button id="manualBtn" class="btn-primary flex flex-col items-center gap-2 py-4 active-method">
                                <i class="fas fa-keyboard text-xl"></i>
                                <span>手动输入</span>
                            </button>
                            <button id="batchBtn" class="btn-outline flex flex-col items-center gap-2 py-4">
                                <i class="fas fa-list text-xl"></i>
                                <span>批量导入</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 手动输入区域 -->
                <div id="manualInput" class="px-4">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">✏️ 手动添加字词</h3>
                        
                        <div class="space-y-4">
                            <!-- 字词输入 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">字词内容</label>
                                <input type="text" class="input-field" placeholder="请输入字词，如：花、太阳" id="wordInput">
                            </div>
                            
                            <!-- 词语输入 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">组词示例（可选）</label>
                                <input type="text" class="input-field" placeholder="如：花朵、花园" id="exampleInput">
                            </div>
                            
                            <!-- 添加按钮 -->
                            <button class="btn-primary w-full flex items-center justify-center gap-2" id="addWordBtn">
                                <i class="fas fa-plus"></i>
                                添加到今日字词
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 批量导入区域 -->
                <div id="batchInput" class="px-4" style="display: none;">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">📋 批量导入字词</h3>
                        
                        <div class="space-y-4">
                            <!-- 文本输入 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">字词列表</label>
                                <textarea class="input-field h-32 resize-none" placeholder="请输入字词，每行一个：&#10;花&#10;草&#10;树&#10;叶" id="batchTextarea"></textarea>
                                <p class="text-xs text-gray-500 mt-1">每行输入一个字词，系统会自动识别并去重</p>
                            </div>
                            
                            <!-- 预设模板 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">快速模板</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <button class="btn-outline text-sm py-2" data-template="nature">
                                        🌿 自然类
                                    </button>
                                    <button class="btn-outline text-sm py-2" data-template="family">
                                        👨‍👩‍👧‍👦 家庭类
                                    </button>
                                    <button class="btn-outline text-sm py-2" data-template="animal">
                                        🐱 动物类
                                    </button>
                                    <button class="btn-outline text-sm py-2" data-template="color">
                                        🎨 颜色类
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 导入按钮 -->
                            <button class="btn-primary w-full flex items-center justify-center gap-2" id="batchAddBtn">
                                <i class="fas fa-upload"></i>
                                批量导入字词
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 今日已添加字词 -->
                <div class="px-4 pb-6">
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-gray-800">📚 今日已添加</h3>
                            <span class="text-sm text-gray-500" id="wordCount">5个字词</span>
                        </div>
                        
                        <div class="space-y-2" id="wordList">
                            <!-- 字词列表 -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <span class="text-xl font-bold">花</span>
                                    <span class="text-sm text-gray-600">花朵</span>
                                </div>
                                <button class="text-red-500 text-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <span class="text-xl font-bold">草</span>
                                    <span class="text-sm text-gray-600">小草</span>
                                </div>
                                <button class="text-red-500 text-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <span class="text-xl font-bold">树</span>
                                    <span class="text-sm text-gray-600">大树</span>
                                </div>
                                <button class="text-red-500 text-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 开始复习按钮 -->
                        <button class="btn-secondary w-full mt-4 flex items-center justify-center gap-2">
                            <i class="fas fa-play"></i>
                            开始复习这些字词
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 模板数据
        const templates = {
            nature: ['花', '草', '树', '叶', '水', '火', '土', '石'],
            family: ['爸', '妈', '我', '家', '爱', '好', '人', '大'],
            animal: ['猫', '狗', '鸟', '鱼', '马', '牛', '羊', '猪'],
            color: ['红', '黄', '蓝', '绿', '白', '黑', '紫', '橙']
        };
        
        let wordCount = 3;
        
        // 切换添加方式
        document.getElementById('manualBtn').addEventListener('click', function() {
            switchMethod('manual');
        });
        
        document.getElementById('batchBtn').addEventListener('click', function() {
            switchMethod('batch');
        });
        
        function switchMethod(method) {
            // 更新按钮样式
            document.querySelectorAll('.active-method').forEach(btn => {
                btn.classList.remove('active-method', 'btn-primary');
                btn.classList.add('btn-outline');
            });
            
            if (method === 'manual') {
                document.getElementById('manualBtn').classList.add('active-method', 'btn-primary');
                document.getElementById('manualBtn').classList.remove('btn-outline');
                document.getElementById('manualInput').style.display = 'block';
                document.getElementById('batchInput').style.display = 'none';
            } else {
                document.getElementById('batchBtn').classList.add('active-method', 'btn-primary');
                document.getElementById('batchBtn').classList.remove('btn-outline');
                document.getElementById('manualInput').style.display = 'none';
                document.getElementById('batchInput').style.display = 'block';
            }
        }
        
        // 模板按钮点击
        document.querySelectorAll('[data-template]').forEach(btn => {
            btn.addEventListener('click', function() {
                const template = this.dataset.template;
                const words = templates[template];
                document.getElementById('batchTextarea').value = words.join('\n');
                
                // 按钮点击效果
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
        
        // 添加字词
        document.getElementById('addWordBtn').addEventListener('click', function() {
            const word = document.getElementById('wordInput').value.trim();
            const example = document.getElementById('exampleInput').value.trim();
            
            if (word) {
                addWordToList(word, example || word);
                document.getElementById('wordInput').value = '';
                document.getElementById('exampleInput').value = '';
            }
            
            // 按钮点击效果
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
        
        // 批量添加
        document.getElementById('batchAddBtn').addEventListener('click', function() {
            const text = document.getElementById('batchTextarea').value.trim();
            if (text) {
                const words = text.split('\n').filter(w => w.trim());
                words.forEach(word => {
                    addWordToList(word.trim(), word.trim());
                });
                document.getElementById('batchTextarea').value = '';
            }
            
            // 按钮点击效果
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
        
        // 添加字词到列表
        function addWordToList(word, example) {
            wordCount++;
            const wordList = document.getElementById('wordList');
            const wordItem = document.createElement('div');
            wordItem.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg fade-in';
            wordItem.innerHTML = `
                <div class="flex items-center gap-3">
                    <span class="text-xl font-bold">${word}</span>
                    <span class="text-sm text-gray-600">${example}</span>
                </div>
                <button class="text-red-500 text-sm delete-btn">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            
            // 添加删除事件
            wordItem.querySelector('.delete-btn').addEventListener('click', function() {
                wordItem.remove();
                wordCount--;
                updateWordCount();
            });
            
            wordList.appendChild(wordItem);
            updateWordCount();
        }
        
        // 更新字词数量
        function updateWordCount() {
            document.getElementById('wordCount').textContent = `${wordCount}个字词`;
        }
        
        // 删除按钮事件
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.closest('.flex').remove();
                wordCount--;
                updateWordCount();
            });
        });
        
        // 通用按钮点击效果
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function() {
                if (!this.style.transform) {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                }
            });
        });
        
        // 返回按钮
        document.querySelector('.nav-back').addEventListener('click', function() {
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    </script>
</body>
</html>
