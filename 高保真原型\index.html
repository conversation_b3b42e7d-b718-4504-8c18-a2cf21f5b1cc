<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小柿子·幼儿识字辅助工具 - 高保真原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="common.css">
    <style>
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(420px, 1fr));
            gap: 30px;
            padding: 30px;
            background: #f5f5f5;
            min-height: 100vh;
        }

        .prototype-item {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            padding: 0;
            width: 390px;
            height: 844px;
            margin: 0 auto;
        }

        .prototype-item:hover {
            transform: translateY(-5px);
        }

        .prototype-title {
            background: var(--primary-green);
            color: white;
            padding: 15px;
            font-weight: bold;
            text-align: center;
            font-size: 1.1rem;
            border-radius: 12px 12px 0 0;
            margin: 0;
        }

        .prototype-frame {
            width: 100%;
            height: 800px;
            border: none;
            background: var(--warm-beige);
            border-radius: 0;
            transform: scale(1);
        }
        
        .header {
            text-align: center;
            padding: 40px 20px;
            background: linear-gradient(135deg, var(--primary-green), var(--light-green));
            color: white;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 页面标题 -->
    <div class="header">
        <h1>🍊 小柿子·幼儿识字辅助工具</h1>
        <p>高保真原型展示 - 所有界面预览</p>
    </div>
    
    <!-- 原型展示网格 -->
    <div class="prototype-grid">
        <!-- 新手引导页 -->
        <div class="prototype-item">
            <div class="prototype-title">📱 新手引导页</div>
            <iframe src="onboarding.html" class="prototype-frame"></iframe>
        </div>
        
        <!-- 首页 -->
        <div class="prototype-item">
            <div class="prototype-title">🏠 首页（今日复习）</div>
            <iframe src="home.html" class="prototype-frame"></iframe>
        </div>
        
        <!-- 复习页 -->
        <div class="prototype-item">
            <div class="prototype-title">📚 复习页（历史复习）</div>
            <iframe src="review.html" class="prototype-frame"></iframe>
        </div>
        
        <!-- 我的页面 -->
        <div class="prototype-item">
            <div class="prototype-title">👤 我的页面</div>
            <iframe src="profile.html" class="prototype-frame"></iframe>
        </div>
        
        <!-- 字词复习详情页 -->
        <div class="prototype-item">
            <div class="prototype-title">✏️ 字词复习详情</div>
            <iframe src="word-review.html" class="prototype-frame"></iframe>
        </div>
        
        <!-- 添加字词页 -->
        <div class="prototype-item">
            <div class="prototype-title">➕ 添加字词</div>
            <iframe src="add-words.html" class="prototype-frame"></iframe>
        </div>
        
        <!-- 设置页面 -->
        <div class="prototype-item">
            <div class="prototype-title">⚙️ 设置页面</div>
            <iframe src="settings.html" class="prototype-frame"></iframe>
        </div>
        
        <!-- 学习记录页 -->
        <div class="prototype-item">
            <div class="prototype-title">📊 学习记录</div>
            <iframe src="learning-records.html" class="prototype-frame"></iframe>
        </div>
        
        <!-- 幼儿信息管理页 -->
        <div class="prototype-item">
            <div class="prototype-title">👶 幼儿信息管理</div>
            <iframe src="child-management.html" class="prototype-frame"></iframe>
        </div>
    </div>
    
    <!-- 说明文档 -->
    <div class="bg-white p-8 m-8 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold mb-4 text-gray-800">📋 原型说明</h2>
        <div class="grid md:grid-cols-2 gap-6">
            <div>
                <h3 class="text-lg font-semibold mb-2 text-green-600">🎯 设计特点</h3>
                <ul class="text-gray-600 space-y-1">
                    <li>• 模拟iPhone 15 Pro界面尺寸 (390x844px)</li>
                    <li>• 低饱和度护眼色调设计</li>
                    <li>• 极简界面，突出核心功能</li>
                    <li>• 优先使用Emoji图标</li>
                    <li>• 遵循微信小程序设计规范</li>
                </ul>
            </div>
            <div>
                <h3 class="text-lg font-semibold mb-2 text-orange-500">⚡ 交互特色</h3>
                <ul class="text-gray-600 space-y-1">
                    <li>• 高效工具定位，减少层级跳转</li>
                    <li>• 核心功能一步直达</li>
                    <li>• 数据风险提示明显但不干扰</li>
                    <li>• 支持离线功能和本地存储</li>
                    <li>• 适配3-6岁幼儿使用习惯</li>
                </ul>
            </div>
        </div>
        
        <div class="mt-6 p-4 bg-orange-50 rounded-lg border-l-4 border-orange-400">
            <h4 class="font-semibold text-orange-800 mb-2">⚠️ 数据存储提示</h4>
            <p class="text-orange-700 text-sm">
                本工具所有学习数据仅保存在您的设备本地，一旦删除本程序或设备故障导致数据丢失，无法通过任何方式找回。
                请定期备份重要数据。
            </p>
        </div>
    </div>
</body>
</html>
